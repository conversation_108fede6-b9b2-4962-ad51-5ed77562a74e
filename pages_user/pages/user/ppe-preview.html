<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防护用品申请页面重构预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #008AFF;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        .category-container {
            padding: 16px;
            background-color: #f5f7fa;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 16px;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ccc;
        }
        
        .empty-text {
            color: #333;
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .empty-subtitle {
            color: #999;
            font-size: 0.9rem;
        }
        
        .category-card {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border-bottom: 1px solid #f0f2f5;
            cursor: pointer;
        }
        
        .category-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            position: relative;
            overflow: hidden;
        }
        
        .category-icon.head {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .category-icon.eye {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .category-icon.respiratory {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .category-icon.hand {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .category-icon.foot {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .category-icon.body {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .category-letter {
            font-size: 16px;
            font-weight: bold;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .category-details {
            flex: 1;
        }
        
        .category-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }
        
        .category-subtitle {
            font-size: 13px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }
        
        .category-count {
            font-size: 12px;
            color: #409EFF;
            background: #e6f4ff;
            padding: 2px 8px;
            border-radius: 10px;
            display: inline-block;
        }
        
        .expand-icon {
            font-size: 14px;
            color: #409EFF;
            font-weight: bold;
        }
        
        .products-grid {
            padding: 0 20px 20px;
        }
        
        .product-card {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            margin-bottom: 12px;
            background: #fafbfc;
            border-radius: 8px;
            border: 1px solid #e8eaed;
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #f6f9ff 0%, #f0f4ff 100%);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
        }

        .product-card.selected::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 0 0 0 8px;
        }
        
        .product-info {
            flex: 1;
        }
        
        .product-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 6px;
        }
        
        .product-spec {
            font-size: 13px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .product-stock {
            font-size: 12px;
            color: #52c41a;
            font-weight: 500;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            background: #f0f2f5;
            border-radius: 20px;
            padding: 2px;
            transition: all 0.3s ease;
        }

        .quantity-selector.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .quantity-selector.active .quantity-display {
            color: white;
            font-weight: 600;
        }
        
        .quantity-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409EFF;
            color: white;
            cursor: pointer;
            border: none;
            font-size: 14px;
        }
        
        .quantity-display {
            padding: 0 12px;
            font-weight: 500;
            min-width: 30px;
            text-align: center;
        }

        .bottom-confirm {
            width: 100%;
            min-height: 5rem;
            box-shadow: 0px -2px 20px rgba(0, 0, 0, 0.1);
            background: linear-gradient(to top, #ffffff 0%, #ffffff 80%, rgba(255, 255, 255, 0.95) 100%);
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            backdrop-filter: blur(10px);
        }

        .bottom-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.2;
            color: #667eea;
            flex: 1;
            font-weight: 600;
        }

        .selected-summary {
            margin-top: 4px;
        }

        .summary-text {
            font-size: 0.8rem;
            color: #999;
            font-weight: 400;
        }

        .bottom-btn {
            min-width: 120px;
            margin-left: 1rem;
        }

        .confirm-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            height: 44px;
            color: white;
            font-weight: 600;
            padding: 0 20px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .confirm-button:active {
            transform: translateY(1px);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            防护用品领用
        </div>
        
        <div class="category-container">
            <!-- 分类卡片示例 -->
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon head">
                            <span class="category-letter">头部</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">头部防护</div>
                            <div class="category-subtitle">个人防护用品 > 头部防护</div>
                            <div class="category-count">3 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
                
                <div class="products-grid">
                    <div class="product-card selected">
                        <div class="product-info">
                            <div class="product-title">安全帽</div>
                            <div class="product-spec">ABS材质 白色</div>
                            <div class="product-stock">库存: 50</div>
                        </div>
                        <div class="quantity-selector active">
                            <button class="quantity-btn">-</button>
                            <span class="quantity-display">2</span>
                            <button class="quantity-btn">+</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon eye">
                            <span class="category-letter">眼部</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">眼部防护</div>
                            <div class="category-subtitle">个人防护用品 > 眼部防护</div>
                            <div class="category-count">2 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon respiratory">
                            <span class="category-letter">呼吸</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">呼吸防护</div>
                            <div class="category-subtitle">个人防护用品 > 呼吸防护</div>
                            <div class="category-count">4 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon hand">
                            <span class="category-letter">手部</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">手部防护</div>
                            <div class="category-subtitle">个人防护用品 > 手部防护</div>
                            <div class="category-count">5 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
            </div>

            <!-- 底部确认区域 -->
            <div class="bottom-confirm">
                <div class="bottom-text">
                    已选择 2 种防护用品
                    <div class="selected-summary">
                        <span class="summary-text">点击确认申请提交申请</span>
                    </div>
                </div>
                <div class="bottom-btn">
                    <button class="confirm-button">确认申请(2)</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
