<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防护用品申请页面重构预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            padding: 20px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #008AFF;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        .category-container {
            padding: 16px;
            background-color: #f5f7fa;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 16px;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .empty-text {
            color: #333;
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .empty-subtitle {
            color: #999;
            font-size: 0.9rem;
        }
        
        .category-card {
            background: #ffffff;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border-bottom: 1px solid #f0f2f5;
            cursor: pointer;
        }
        
        .category-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            position: relative;
            overflow: hidden;
        }
        
        .category-icon.head {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .category-icon.eye {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .category-icon.respiratory {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .category-icon.hand {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .category-icon.foot {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .category-icon.body {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .category-letter {
            position: absolute;
            top: 2px;
            left: 4px;
            font-size: 12px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.8);
            z-index: 1;
        }
        
        .category-emoji {
            font-size: 24px;
            z-index: 2;
        }
        
        .category-details {
            flex: 1;
        }
        
        .category-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }
        
        .category-subtitle {
            font-size: 13px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }
        
        .category-count {
            font-size: 12px;
            color: #409EFF;
            background: #e6f4ff;
            padding: 2px 8px;
            border-radius: 10px;
            display: inline-block;
        }
        
        .expand-icon {
            font-size: 14px;
            color: #409EFF;
            font-weight: bold;
        }
        
        .products-grid {
            padding: 0 20px 20px;
        }
        
        .product-card {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            margin-bottom: 12px;
            background: #fafbfc;
            border-radius: 8px;
            border: 1px solid #e8eaed;
        }
        
        .product-info {
            flex: 1;
        }
        
        .product-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 6px;
        }
        
        .product-spec {
            font-size: 13px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .product-stock {
            font-size: 12px;
            color: #52c41a;
            font-weight: 500;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            background: #f0f2f5;
            border-radius: 20px;
            padding: 2px;
        }
        
        .quantity-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409EFF;
            color: white;
            cursor: pointer;
            border: none;
            font-size: 14px;
        }
        
        .quantity-display {
            padding: 0 12px;
            font-weight: 500;
            min-width: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            防护用品领用
        </div>
        
        <div class="category-container">
            <!-- 分类卡片示例 -->
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon head">
                            <span class="category-letter">头</span>
                            <span class="category-emoji">🪖</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">头部防护</div>
                            <div class="category-subtitle">个人防护用品 > 头部防护</div>
                            <div class="category-count">3 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
                
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-info">
                            <div class="product-title">安全帽</div>
                            <div class="product-spec">ABS材质 白色</div>
                            <div class="product-stock">库存: 50</div>
                        </div>
                        <div class="quantity-selector">
                            <button class="quantity-btn">-</button>
                            <span class="quantity-display">0</span>
                            <button class="quantity-btn">+</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon eye">
                            <span class="category-letter">眼</span>
                            <span class="category-emoji">👓</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">眼部防护</div>
                            <div class="category-subtitle">个人防护用品 > 眼部防护</div>
                            <div class="category-count">2 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon respiratory">
                            <span class="category-letter">呼</span>
                            <span class="category-emoji">😷</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">呼吸防护</div>
                            <div class="category-subtitle">个人防护用品 > 呼吸防护</div>
                            <div class="category-count">4 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-header">
                    <div class="category-info">
                        <div class="category-icon hand">
                            <span class="category-letter">手</span>
                            <span class="category-emoji">🧤</span>
                        </div>
                        <div class="category-details">
                            <div class="category-title">手部防护</div>
                            <div class="category-subtitle">个人防护用品 > 手部防护</div>
                            <div class="category-count">5 种产品</div>
                        </div>
                    </div>
                    <span class="expand-icon">▼</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
